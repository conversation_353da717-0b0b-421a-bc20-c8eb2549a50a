import { create } from 'zustand';
import { supabase } from '../utils/supabaseClient';

export const useTenantListingsStore = create((set) => ({
  tenants: [],
  loading: false,
  error: null,
  fetchTenants: async () => {
    try {
      set({ loading: true, error: null });
      const { data, error } = await supabase
        .from('tenant_listings')
        .select('*')
        .order('tenant_name', { ascending: true });
      if (error) throw error;
      set({ tenants: data || [], loading: false });
    } catch {
      set({ error: 'Error loading tenant listings', loading: false });
    }
  },
})); 