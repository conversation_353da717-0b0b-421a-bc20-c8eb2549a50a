import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, Edit2, Trash2, Search, Building2, Phone, Tag } from 'lucide-react';
import { useTenantListingsStore } from '../../store/useTenantListingsStore';
import TenantForm from '../../components/admin/TenantForm';
import UnitsDisplay from '../../components/UnitsDisplay';
import Loader from '../../components/Loader';

const Container = styled(motion.div)`
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
`;

const Title = styled.h1`
  color: #2c3e50;
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
`;

const Controls = styled.div`
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
`;

const SearchContainer = styled.div`
  position: relative;
  min-width: 300px;
`;

const SearchInput = styled.input`
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 1rem;
  transition: all 0.2s;

  &:focus {
    outline: none;
    border-color: #F16925;
    box-shadow: 0 0 0 2px rgba(241, 105, 37, 0.1);
  }
`;

const SearchIcon = styled.div`
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
`;

const AddButton = styled(motion.button)`
  background: #F16925;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s;

  &:hover {
    background: #e55a1f;
  }
`;

const TableContainer = styled.div`
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
`;

const Th = styled.th`
  padding: 1rem;
  text-align: left;
  background: #f8f9fa;
  color: #495057;
  font-weight: 600;
  border-bottom: 2px solid #dee2e6;
  white-space: nowrap;
`;

const Td = styled.td`
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
  color: #2c3e50;
  vertical-align: top;
`;

const Tr = styled(motion.tr)`
  &:hover {
    background: #f8f9fa;
  }
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const ActionButton = styled(motion.button)`
  padding: 0.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
`;

const EditButton = styled(ActionButton)`
  background: #007bff;
  color: white;

  &:hover {
    background: #0056b3;
  }
`;

const DeleteButton = styled(ActionButton)`
  background: #dc3545;
  color: white;

  &:hover {
    background: #c82333;
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 3rem;
  color: #6c757d;
`;

const ErrorMessage = styled.div`
  background: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 1rem;
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem;
`;

const Stats = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
`;

const StatCard = styled.div`
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const StatValue = styled.div`
  font-size: 2rem;
  font-weight: 700;
  color: #F16925;
`;

const StatLabel = styled.div`
  color: #6c757d;
  font-size: 0.875rem;
  margin-top: 0.25rem;
`;

const TenantManagement = () => {
  const { 
    tenants, 
    loading, 
    error, 
    fetchTenants, 
    createTenant, 
    updateTenant, 
    deleteTenant,
    clearError 
  } = useTenantListingsStore();

  const [searchTerm, setSearchTerm] = useState('');
  const [showForm, setShowForm] = useState(false);
  const [editingTenant, setEditingTenant] = useState(null);
  const [formLoading, setFormLoading] = useState(false);

  useEffect(() => {
    fetchTenants();
  }, [fetchTenants]);

  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        clearError();
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, clearError]);

  const filteredTenants = tenants.filter(tenant =>
    tenant.tenant_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (tenant.units && tenant.units.some(unit => 
      unit.toLowerCase().includes(searchTerm.toLowerCase())
    )) ||
    (tenant.unit && tenant.unit.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (tenant.category && tenant.category.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleAddTenant = () => {
    setEditingTenant(null);
    setShowForm(true);
  };

  const handleEditTenant = (tenant) => {
    setEditingTenant(tenant);
    setShowForm(true);
  };

  const handleDeleteTenant = async (tenant) => {
    if (window.confirm(`Are you sure you want to delete "${tenant.tenant_name}"?`)) {
      await deleteTenant(tenant.id);
    }
  };

  const handleFormSubmit = async (formData) => {
    setFormLoading(true);
    try {
      if (editingTenant) {
        await updateTenant(editingTenant.id, formData);
      } else {
        await createTenant(formData);
      }
      setShowForm(false);
      setEditingTenant(null);
    } catch (error) {
      console.error('Error saving tenant:', error);
    } finally {
      setFormLoading(false);
    }
  };

  const handleFormCancel = () => {
    setShowForm(false);
    setEditingTenant(null);
  };

  // Calculate stats
  const totalTenants = tenants.length;
  const retailTenants = tenants.filter(t => t.category === 'Retail').length;
  const beautyTenants = tenants.filter(t => t.category === 'Beauty').length;
  const totalUnits = tenants.reduce((acc, tenant) => {
    if (Array.isArray(tenant.units)) {
      return acc + tenant.units.length;
    }
    return acc + (tenant.unit ? 1 : 0);
  }, 0);

  if (loading && tenants.length === 0) {
    return (
      <Container>
        <LoadingContainer>
          <Loader />
        </LoadingContainer>
      </Container>
    );
  }

  return (
    <Container
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Header>
        <Title>Tenant Management</Title>
        <Controls>
          <SearchContainer>
            <SearchIcon>
              <Search size={18} />
            </SearchIcon>
            <SearchInput
              type="text"
              placeholder="Search tenants, units, or categories..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </SearchContainer>
          <AddButton
            onClick={handleAddTenant}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Plus size={18} />
            Add Tenant
          </AddButton>
        </Controls>
      </Header>

      <Stats>
        <StatCard>
          <StatValue>{totalTenants}</StatValue>
          <StatLabel>Total Tenants</StatLabel>
        </StatCard>
        <StatCard>
          <StatValue>{totalUnits}</StatValue>
          <StatLabel>Total Units</StatLabel>
        </StatCard>
        <StatCard>
          <StatValue>{retailTenants}</StatValue>
          <StatLabel>Retail Tenants</StatLabel>
        </StatCard>
        <StatCard>
          <StatValue>{beautyTenants}</StatValue>
          <StatLabel>Beauty Tenants</StatLabel>
        </StatCard>
      </Stats>

      {error && (
        <ErrorMessage>
          {error}
        </ErrorMessage>
      )}

      <TableContainer>
        <Table>
          <thead>
            <tr>
              <Th>Tenant Name</Th>
              <Th>Units</Th>
              <Th>Type</Th>
              <Th>Phone</Th>
              <Th>Category</Th>
              <Th>Actions</Th>
            </tr>
          </thead>
          <tbody>
            {filteredTenants.length === 0 ? (
              <tr>
                <td colSpan="6">
                  <EmptyState>
                    {searchTerm ? 'No tenants found matching your search.' : 'No tenants found.'}
                  </EmptyState>
                </td>
              </tr>
            ) : (
              filteredTenants.map((tenant) => (
                <Tr
                  key={tenant.id}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                >
                  <Td>{tenant.tenant_name}</Td>
                  <Td>
                    <UnitsDisplay 
                      units={tenant.units}
                      fallbackUnit={tenant.unit}
                      variant="badges"
                      showIcon={false}
                    />
                  </Td>
                  <Td>{tenant.unit_type || '-'}</Td>
                  <Td>{tenant.phone_default || '-'}</Td>
                  <Td>{tenant.category}</Td>
                  <Td>
                    <ActionButtons>
                      <EditButton
                        onClick={() => handleEditTenant(tenant)}
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                      >
                        <Edit2 size={14} />
                      </EditButton>
                      <DeleteButton
                        onClick={() => handleDeleteTenant(tenant)}
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                      >
                        <Trash2 size={14} />
                      </DeleteButton>
                    </ActionButtons>
                  </Td>
                </Tr>
              ))
            )}
          </tbody>
        </Table>
      </TableContainer>

      <AnimatePresence>
        {showForm && (
          <TenantForm
            tenant={editingTenant}
            onSubmit={handleFormSubmit}
            onCancel={handleFormCancel}
            loading={formLoading}
          />
        )}
      </AnimatePresence>
    </Container>
  );
};

export default TenantManagement;
