import React, { useEffect } from 'react';
import styled from 'styled-components';
import { Navbar } from '../components/Navbar';
import { Footer } from '../components/Footer';
import { useTenantListingsStore } from '../store/useTenantListingsStore';
import UnitsDisplay from '../components/UnitsDisplay';

const PageWrapper = styled.div`
  background-color: #f8f9fa;
  min-height: 100vh;
`;

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
`;

const Title = styled.h1`
  font-size: 2.2rem;
  color: #F16925;
  font-weight: 900;
  margin-bottom: 2rem;
`;

const TestSection = styled.div`
  background: white;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const SectionTitle = styled.h2`
  color: #2c3e50;
  font-size: 1.5rem;
  margin-bottom: 1rem;
`;

const TestCase = styled.div`
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  background: #f8f9fa;
`;

const TestLabel = styled.div`
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
`;

const TestResult = styled.div`
  margin-top: 0.5rem;
`;

const TenantCard = styled.div`
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
`;

const TenantName = styled.h3`
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
`;

const TenantInfo = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
`;

const InfoItem = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
`;

const InfoLabel = styled.span`
  font-size: 0.75rem;
  color: #6c757d;
  font-weight: 600;
  text-transform: uppercase;
`;

const InfoValue = styled.span`
  color: #495057;
`;

const TenantTest = () => {
  const { tenants, loading, error, fetchTenants } = useTenantListingsStore();

  useEffect(() => {
    fetchTenants();
  }, [fetchTenants]);

  // Test data for UnitsDisplay component
  const testCases = [
    {
      label: 'Single unit (array)',
      units: ['101'],
      fallbackUnit: null
    },
    {
      label: 'Multiple units (array)',
      units: ['301', '302', '304'],
      fallbackUnit: null
    },
    {
      label: 'Fallback to single unit field',
      units: null,
      fallbackUnit: '205'
    },
    {
      label: 'Comma-separated in fallback',
      units: null,
      fallbackUnit: '401, 402, 403'
    },
    {
      label: 'Empty units',
      units: [],
      fallbackUnit: null
    },
    {
      label: 'Mixed empty and valid units',
      units: ['501', '', '502', '   ', '503'],
      fallbackUnit: null
    }
  ];

  // Find examples of different unit configurations from real data
  const singleUnitTenants = tenants.filter(t => 
    (Array.isArray(t.units) && t.units.length === 1) ||
    (!Array.isArray(t.units) && t.unit && !t.unit.includes(','))
  ).slice(0, 3);

  const multiUnitTenants = tenants.filter(t => 
    (Array.isArray(t.units) && t.units.length > 1) ||
    (!Array.isArray(t.units) && t.unit && t.unit.includes(','))
  ).slice(0, 3);

  if (loading) {
    return (
      <PageWrapper>
        <Navbar />
        <Container>
          <Title>Loading tenant data...</Title>
        </Container>
        <Footer />
      </PageWrapper>
    );
  }

  if (error) {
    return (
      <PageWrapper>
        <Navbar />
        <Container>
          <Title>Error: {error}</Title>
        </Container>
        <Footer />
      </PageWrapper>
    );
  }

  return (
    <PageWrapper>
      <Navbar />
      <Container>
        <Title>Tenant Units Display Test</Title>

        <TestSection>
          <SectionTitle>UnitsDisplay Component Test Cases</SectionTitle>
          {testCases.map((testCase, index) => (
            <TestCase key={index}>
              <TestLabel>{testCase.label}</TestLabel>
              <div>
                <strong>Data:</strong> units={JSON.stringify(testCase.units)}, fallbackUnit="{testCase.fallbackUnit}"
              </div>
              <TestResult>
                <div style={{ marginBottom: '0.5rem' }}>
                  <strong>Badges variant:</strong> <UnitsDisplay units={testCase.units} fallbackUnit={testCase.fallbackUnit} variant="badges" />
                </div>
                <div style={{ marginBottom: '0.5rem' }}>
                  <strong>Text variant:</strong> <UnitsDisplay units={testCase.units} fallbackUnit={testCase.fallbackUnit} variant="text" />
                </div>
                <div>
                  <strong>Compact variant:</strong> <UnitsDisplay units={testCase.units} fallbackUnit={testCase.fallbackUnit} variant="compact" />
                </div>
              </TestResult>
            </TestCase>
          ))}
        </TestSection>

        <TestSection>
          <SectionTitle>Real Data Examples - Single Unit Tenants</SectionTitle>
          {singleUnitTenants.map((tenant) => (
            <TenantCard key={tenant.id}>
              <TenantName>{tenant.tenant_name}</TenantName>
              <TenantInfo>
                <InfoItem>
                  <InfoLabel>Units (Badges)</InfoLabel>
                  <InfoValue>
                    <UnitsDisplay 
                      units={tenant.units}
                      fallbackUnit={tenant.unit}
                      variant="badges"
                    />
                  </InfoValue>
                </InfoItem>
                <InfoItem>
                  <InfoLabel>Units (Text)</InfoLabel>
                  <InfoValue>
                    <UnitsDisplay 
                      units={tenant.units}
                      fallbackUnit={tenant.unit}
                      variant="text"
                    />
                  </InfoValue>
                </InfoItem>
                <InfoItem>
                  <InfoLabel>Category</InfoLabel>
                  <InfoValue>{tenant.category}</InfoValue>
                </InfoItem>
                <InfoItem>
                  <InfoLabel>Phone</InfoLabel>
                  <InfoValue>{tenant.phone_default || 'N/A'}</InfoValue>
                </InfoItem>
              </TenantInfo>
            </TenantCard>
          ))}
        </TestSection>

        <TestSection>
          <SectionTitle>Real Data Examples - Multi-Unit Tenants</SectionTitle>
          {multiUnitTenants.map((tenant) => (
            <TenantCard key={tenant.id}>
              <TenantName>{tenant.tenant_name}</TenantName>
              <TenantInfo>
                <InfoItem>
                  <InfoLabel>Units (Badges)</InfoLabel>
                  <InfoValue>
                    <UnitsDisplay 
                      units={tenant.units}
                      fallbackUnit={tenant.unit}
                      variant="badges"
                    />
                  </InfoValue>
                </InfoItem>
                <InfoItem>
                  <InfoLabel>Units (Text)</InfoLabel>
                  <InfoValue>
                    <UnitsDisplay 
                      units={tenant.units}
                      fallbackUnit={tenant.unit}
                      variant="text"
                    />
                  </InfoValue>
                </InfoItem>
                <InfoItem>
                  <InfoLabel>Category</InfoLabel>
                  <InfoValue>{tenant.category}</InfoValue>
                </InfoItem>
                <InfoItem>
                  <InfoLabel>Phone</InfoLabel>
                  <InfoValue>{tenant.phone_default || 'N/A'}</InfoValue>
                </InfoItem>
              </TenantInfo>
            </TenantCard>
          ))}
        </TestSection>

        <TestSection>
          <SectionTitle>Data Summary</SectionTitle>
          <div>
            <p><strong>Total Tenants:</strong> {tenants.length}</p>
            <p><strong>Tenants with units array:</strong> {tenants.filter(t => Array.isArray(t.units)).length}</p>
            <p><strong>Tenants with multiple units:</strong> {tenants.filter(t => 
              (Array.isArray(t.units) && t.units.length > 1) ||
              (!Array.isArray(t.units) && t.unit && t.unit.includes(','))
            ).length}</p>
            <p><strong>Retail tenants:</strong> {tenants.filter(t => t.category === 'Retail').length}</p>
            <p><strong>Beauty tenants:</strong> {tenants.filter(t => t.category === 'Beauty').length}</p>
          </div>
        </TestSection>
      </Container>
      <Footer />
    </PageWrapper>
  );
};

export default TenantTest;
