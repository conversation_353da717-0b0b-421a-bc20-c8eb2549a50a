-- Add units array column to store multiple unit numbers per tenant listing
-- Generated: 20250706140000_add_units_column_tenant_listings.sql

BEGIN;

-- 1) Ensure the table exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_schema = 'public'
          AND table_name   = 'tenant_listings'
    ) THEN
        RAISE EXCEPTION 'Table public.tenant_listings does not exist';
    END IF;
END $$;

-- 2) Add the new column if it does not already exist
ALTER TABLE public.tenant_listings
    ADD COLUMN IF NOT EXISTS units TEXT[];

-- 3) Migrate existing single-value "unit" column (if present) into the new array column
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public'
          AND table_name   = 'tenant_listings'
          AND column_name  = 'unit'
    ) THEN
        UPDATE public.tenant_listings
        SET units = ARRAY[unit]::TEXT[]
        WHERE units IS NULL AND unit IS NOT NULL;
    END IF;
END $$;

-- 4) (Optional) Drop the legacy column when you are 100% sure nothing relies on it
-- ALTER TABLE public.tenant_listings DROP COLUMN IF EXISTS unit;

COMMIT; 