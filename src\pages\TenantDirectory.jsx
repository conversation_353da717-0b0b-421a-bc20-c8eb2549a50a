import React, { useEffect } from 'react';
import styled from 'styled-components';
import { Navbar } from '../components/Navbar';
import { Footer } from '../components/Footer';
import { useTenantListingsStore } from '../store/useTenantListingsStore';
import UnitsDisplay from '../components/UnitsDisplay';

const PageWrapper = styled.div`
  background-color: #f8f9fa;
  min-height: 100vh;
`;

const DirectoryContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
`;

const Title = styled.h1`
  font-size: 2.2rem;
  color: #F16925;
  font-weight: 900;
  margin-bottom: 2rem;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const Th = styled.th`
  padding: 1rem;
  text-align: left;
  background: #f8f9fa;
  color: #495057;
  font-weight: 600;
  border-bottom: 2px solid #dee2e6;
`;

const Td = styled.td`
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
  color: #2c3e50;
`;

const UnitsTd = styled(Td)`
  min-width: 150px;
`;

const Tr = styled.tr`
  &:hover {
    background: #f8f9fa;
  }
`;

const Loading = styled.div`
  text-align: center;
  padding: 2rem;
  color: #666;
`;

const TenantDirectory = () => {
  const { tenants, loading, error, fetchTenants } = useTenantListingsStore();

  useEffect(() => {
    console.log('TenantDirectory mounted, fetching tenants...');
    fetchTenants();
  }, [fetchTenants]);

  console.log('TenantDirectory rendered. Tenants:', tenants);

  return (
    <>
      <Navbar />
      <PageWrapper>
        <DirectoryContainer>
          <Title>Tenant Directory</Title>
          {loading ? (
            <Loading>Loading tenants...</Loading>
          ) : error ? (
            <Loading>{error}</Loading>
          ) : (
            <Table>
              <thead>
                <tr>
                  <Th>Tenant Name</Th>
                  <Th>Unit</Th>
                  <Th>Unit Type</Th>
                  <Th>Phone</Th>
                  <Th>Category</Th>
                </tr>
              </thead>
              <tbody>
                {tenants.map((tenant) => (
                  <Tr key={tenant.id}>
                    <Td>{tenant.tenant_name}</Td>
                    <UnitsTd>
                      <UnitsDisplay
                        units={tenant.units}
                        fallbackUnit={tenant.unit}
                        variant="badges"
                        showIcon={false}
                      />
                    </UnitsTd>
                    <Td>{tenant.unit_type}</Td>
                    <Td>{tenant.phone_default}</Td>
                    <Td>{tenant.category}</Td>
                  </Tr>
                ))}
              </tbody>
            </Table>
          )}
        </DirectoryContainer>
      </PageWrapper>
      <Footer />
    </>
  );
};

export default TenantDirectory; 