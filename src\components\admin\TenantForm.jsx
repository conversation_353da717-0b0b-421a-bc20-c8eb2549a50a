import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { X, Plus, Trash2, Building2, Phone, Tag, User } from 'lucide-react';

const Overlay = styled(motion.div)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
`;

const Modal = styled(motion.div)`
  background: white;
  border-radius: 12px;
  padding: 2rem;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
`;

const Title = styled.h2`
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.2s;

  &:hover {
    background: #f8f9fa;
    color: #495057;
  }
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const Label = styled.label`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #495057;
  font-weight: 600;
  font-size: 0.875rem;
`;

const Input = styled.input`
  padding: 0.75rem;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 1rem;
  transition: all 0.2s;

  &:focus {
    outline: none;
    border-color: #F16925;
    box-shadow: 0 0 0 2px rgba(241, 105, 37, 0.1);
  }

  &:disabled {
    background: #f8f9fa;
    color: #6c757d;
  }
`;

const Select = styled.select`
  padding: 0.75rem;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 1rem;
  background: white;
  transition: all 0.2s;

  &:focus {
    outline: none;
    border-color: #F16925;
    box-shadow: 0 0 0 2px rgba(241, 105, 37, 0.1);
  }
`;

const UnitsSection = styled.div`
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 1rem;
  background: #f8f9fa;
`;

const UnitsHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
`;

const UnitsTitle = styled.h4`
  margin: 0;
  color: #495057;
  font-size: 0.875rem;
  font-weight: 600;
`;

const AddUnitButton = styled.button`
  background: #F16925;
  color: white;
  border: none;
  padding: 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  transition: all 0.2s;

  &:hover {
    background: #e55a1f;
  }
`;

const UnitRow = styled.div`
  display: flex;
  gap: 0.5rem;
  align-items: center;
  margin-bottom: 0.5rem;

  &:last-child {
    margin-bottom: 0;
  }
`;

const UnitInput = styled(Input)`
  flex: 1;
`;

const RemoveUnitButton = styled.button`
  background: #dc3545;
  color: white;
  border: none;
  padding: 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.2s;

  &:hover {
    background: #c82333;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1rem;
`;

const Button = styled.button`
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const PrimaryButton = styled(Button)`
  background: #F16925;
  color: white;

  &:hover {
    background: #e55a1f;
  }

  &:disabled {
    background: #6c757d;
    cursor: not-allowed;
  }
`;

const SecondaryButton = styled(Button)`
  background: #6c757d;
  color: white;

  &:hover {
    background: #5a6268;
  }
`;

const ErrorMessage = styled.div`
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.25rem;
`;

const TenantForm = ({ tenant, onSubmit, onCancel, loading }) => {
  const [formData, setFormData] = useState({
    tenant_name: '',
    units: [''],
    unit_type: '',
    phone_default: '',
    category: 'Retail',
    avatar: '',
    banner_url: '',
    slug: ''
  });
  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (tenant) {
      setFormData({
        tenant_name: tenant.tenant_name || '',
        units: Array.isArray(tenant.units) && tenant.units.length > 0 
          ? tenant.units 
          : [tenant.unit || ''],
        unit_type: tenant.unit_type || '',
        phone_default: tenant.phone_default || '',
        category: tenant.category || 'Retail',
        avatar: tenant.avatar || '',
        banner_url: tenant.banner_url || '',
        slug: tenant.slug || ''
      });
    }
  }, [tenant]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }
  };

  const handleUnitChange = (index, value) => {
    const newUnits = [...formData.units];
    newUnits[index] = value;
    setFormData(prev => ({ ...prev, units: newUnits }));
  };

  const addUnit = () => {
    setFormData(prev => ({ ...prev, units: [...prev.units, ''] }));
  };

  const removeUnit = (index) => {
    if (formData.units.length > 1) {
      const newUnits = formData.units.filter((_, i) => i !== index);
      setFormData(prev => ({ ...prev, units: newUnits }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.tenant_name.trim()) {
      newErrors.tenant_name = 'Tenant name is required';
    }
    
    const validUnits = formData.units.filter(unit => unit.trim() !== '');
    if (validUnits.length === 0) {
      newErrors.units = 'At least one unit is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (validateForm()) {
      // Filter out empty units before submitting
      const cleanedData = {
        ...formData,
        units: formData.units.filter(unit => unit.trim() !== '')
      };
      onSubmit(cleanedData);
    }
  };

  return (
    <Overlay
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onClick={(e) => e.target === e.currentTarget && onCancel()}
    >
      <Modal
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
      >
        <Header>
          <Title>{tenant ? 'Edit Tenant' : 'Add New Tenant'}</Title>
          <CloseButton onClick={onCancel}>
            <X size={20} />
          </CloseButton>
        </Header>

        <Form onSubmit={handleSubmit}>
          <FormGroup>
            <Label>
              <User size={16} />
              Tenant Name *
            </Label>
            <Input
              type="text"
              name="tenant_name"
              value={formData.tenant_name}
              onChange={handleInputChange}
              placeholder="Enter tenant name"
              disabled={loading}
            />
            {errors.tenant_name && <ErrorMessage>{errors.tenant_name}</ErrorMessage>}
          </FormGroup>

          <FormGroup>
            <Label>
              <Building2 size={16} />
              Units *
            </Label>
            <UnitsSection>
              <UnitsHeader>
                <UnitsTitle>Unit Numbers</UnitsTitle>
                <AddUnitButton type="button" onClick={addUnit}>
                  <Plus size={12} />
                  Add Unit
                </AddUnitButton>
              </UnitsHeader>
              {formData.units.map((unit, index) => (
                <UnitRow key={index}>
                  <UnitInput
                    type="text"
                    value={unit}
                    onChange={(e) => handleUnitChange(index, e.target.value)}
                    placeholder={`Unit ${index + 1}`}
                    disabled={loading}
                  />
                  {formData.units.length > 1 && (
                    <RemoveUnitButton
                      type="button"
                      onClick={() => removeUnit(index)}
                    >
                      <Trash2 size={14} />
                    </RemoveUnitButton>
                  )}
                </UnitRow>
              ))}
            </UnitsSection>
            {errors.units && <ErrorMessage>{errors.units}</ErrorMessage>}
          </FormGroup>

          <FormGroup>
            <Label>
              <Tag size={16} />
              Unit Type
            </Label>
            <Input
              type="text"
              name="unit_type"
              value={formData.unit_type}
              onChange={handleInputChange}
              placeholder="e.g., Retail, Beauty suite"
              disabled={loading}
            />
          </FormGroup>

          <FormGroup>
            <Label>
              <Phone size={16} />
              Phone Number
            </Label>
            <Input
              type="tel"
              name="phone_default"
              value={formData.phone_default}
              onChange={handleInputChange}
              placeholder="(*************"
              disabled={loading}
            />
          </FormGroup>

          <FormGroup>
            <Label>Category</Label>
            <Select
              name="category"
              value={formData.category}
              onChange={handleInputChange}
              disabled={loading}
            >
              <option value="Retail">Retail</option>
              <option value="Beauty">Beauty</option>
            </Select>
          </FormGroup>

          <ButtonGroup>
            <SecondaryButton type="button" onClick={onCancel} disabled={loading}>
              Cancel
            </SecondaryButton>
            <PrimaryButton type="submit" disabled={loading}>
              {loading ? 'Saving...' : (tenant ? 'Update Tenant' : 'Create Tenant')}
            </PrimaryButton>
          </ButtonGroup>
        </Form>
      </Modal>
    </Overlay>
  );
};

export default TenantForm;
